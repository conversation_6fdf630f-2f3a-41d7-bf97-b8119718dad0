<!-- Modern App Layout with Angular Material -->
<mat-sidenav-container class="app-container">
  <!-- Navigation Drawer -->
  <mat-sidenav
    #drawer
    [mode]="isHandset ? 'over' : 'side'"
    [opened]="!isHandset"
    class="app-drawer"
    fixedInViewport="true"
    [disableClose]="!isHandset"
  >
    <!-- Drawer Header -->
    <mat-toolbar class="drawer-header">
      <mat-icon class="app-icon">storage</mat-icon>
      <span class="app-title">NS Drive</span>
    </mat-toolbar>

    <!-- Navigation Menu -->
    <mat-nav-list class="nav-list">
      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openHome()"
        (keydown.enter)="openHome()"
        (keydown.space)="openHome()"
        [activated]="(tab$ | async) === 'home'"
        class="nav-item"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>dashboard</mat-icon>
        <span matListItemTitle>Dashboard</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openProfiles()"
        (keydown.enter)="openProfiles()"
        (keydown.space)="openProfiles()"
        [activated]="(tab$ | async) === 'profiles'"
        class="nav-item"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>folder_shared</mat-icon>
        <span matListItemTitle>Sync Profiles</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openRemotes()"
        (keydown.enter)="openRemotes()"
        (keydown.space)="openRemotes()"
        [activated]="(tab$ | async) === 'remotes'"
        class="nav-item"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>cloud_queue</mat-icon>
        <span matListItemTitle>Cloud Storage</span>
      </a>
    </mat-nav-list>

    <!-- Drawer Footer -->
    <div class="drawer-footer">
      <mat-divider></mat-divider>
      <div class="version-info">
        <mat-icon class="version-icon">info</mat-icon>
        <span>Version 1.0.0</span>
      </div>
    </div>
  </mat-sidenav>

  <!-- Main Content -->
  <mat-sidenav-content class="main-content">
    <!-- App Bar -->
    <mat-toolbar color="primary" class="app-toolbar">
      <button
        mat-icon-button
        (click)="drawer.toggle()"
        class="menu-button"
        [class.hidden]="!isHandset"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <span class="toolbar-title">
        {{
          (tab$ | async) === "home"
            ? "Dashboard"
            : (tab$ | async) === "profiles"
            ? "Sync Profiles"
            : (tab$ | async) === "remotes"
            ? "Cloud Storage"
            : "NS Drive"
        }}
      </span>

      <span class="toolbar-spacer"></span>

      <!-- Action Buttons -->
      <button mat-icon-button matTooltip="Settings">
        <mat-icon>settings</mat-icon>
      </button>

      <button mat-icon-button matTooltip="Help">
        <mat-icon>help</mat-icon>
      </button>
    </mat-toolbar>

    <!-- Page Content Container -->
    <main class="page-content" [ngSwitch]="tab$ | async">
      <app-home *ngSwitchCase="'home'"></app-home>
      <app-profiles *ngSwitchCase="'profiles'"></app-profiles>
      <app-remotes *ngSwitchCase="'remotes'"></app-remotes>
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
