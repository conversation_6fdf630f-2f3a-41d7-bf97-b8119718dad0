// Angular Material Theme - Using prebuilt theme with proper dark mode support
@import "@angular/material/prebuilt-themes/azure-blue.css";

// Global styles - minimal setup for Angular Material
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
}

// App Layout Styles
.app-container {
  height: 100vh;
  width: 100vw;
}

.app-drawer {
  width: 280px;
}

.drawer-header {
  background: transparent;
  color: inherit;
  padding: 0 16px;
  min-height: 64px;
}

.app-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  margin-right: 12px;
}

.app-title {
  font-size: 20px;
  font-weight: 500;
}

.nav-list {
  padding: 8px 0;
}

.nav-item {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 8px;
}

.version-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-button {
  margin-right: 8px;

  &.hidden {
    display: none;
  }
}

.toolbar-title {
  font-size: 20px;
  font-weight: 500;
}

.toolbar-spacer {
  flex: 1 1 auto;
}

.page-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background-color: #fafafa;
}

// Home Component Styles
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.welcome-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;
}

.welcome-card {
  max-width: 600px;
  text-align: center;
}

.welcome-avatar {
  background-color: #3f51b5;
  color: white;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(63, 81, 181, 0.04);
}

.operations-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.operations-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
}

.header-content {
  flex: 1;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 500;
}

.header-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.header-subtitle {
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}

.operations-tabs {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
}

.operation-tab {
  display: flex;
  align-items: center;
  min-width: 200px;
  max-width: 300px;
  padding: 12px 16px;
  border: 2px solid rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;

  &.active {
    border-color: #3f51b5;
    background-color: rgba(63, 81, 181, 0.08);
  }

  &.running {
    border-left: 4px solid #ff9800;
  }
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.tab-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;

  &.active-icon {
    color: #3f51b5;
  }
}

.tab-name {
  flex: 1;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-indicator {
  font-size: 12px;
  width: 12px;
  height: 12px;
  color: #4caf50;
}

.tab-menu-button {
  width: 32px;
  height: 32px;
}

.tab-edit-mode {
  width: 100%;
}

.operations-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.operation-content {
  flex-direction: column;
  gap: 24px;
  display: none;

  &.active {
    display: flex;
  }
}

.control-panel {
  .control-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.profile-select {
  width: 100%;
  margin-bottom: 24px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.action-button {
  height: 56px;
  font-size: 16px;
  font-weight: 500;
}

.action-icon {
  margin-right: 8px;
}

// Profiles Component Styles
.profiles-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  overflow-y: auto;
}

.profiles-header {
  flex-shrink: 0;
}

.spacer {
  flex: 1;
}

.profiles-content {
  flex: 1;
}

.profiles-accordion {
  .profile-panel {
    margin-bottom: 20px;
  }
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-name {
  font-weight: 500;
}

.profile-description {
  font-weight: normal;
  font-style: italic;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

.profile-form {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  .full-width {
    width: 100%;
    margin-top: 16px;
  }
}

// Remotes Component Styles
.remotes-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  overflow-y: auto;
}

.remotes-header {
  flex-shrink: 0;
}

.remote-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

// Utility Classes
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.flex-1 {
  flex: 1;
}

.gap-8 {
  gap: 8px;
}

.gap-12 {
  gap: 12px;
}

.gap-16 {
  gap: 16px;
}

.gap-24 {
  gap: 24px;
}
